'use client';

import React, { useState } from 'react';
import AdminLayout from '@/components/AdminLayout';
import { ProtectedRoute } from '@/contexts/AuthContext';
import { useListData, useFormSubmit, useModal, useForm } from '@/lib/hooks';
import { bannerAPI } from '@/lib/api';
import { formatDate, truncateText, getInputClassName } from '@/lib/utils';
import BannerImageUpload from '@/components/BannerImageUpload';

interface Banner {
  id: number;
  title: string;
  description?: string;
  image_url?: string;
  link_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface BannerForm {
  title: string;
  description: string;
  image_url: string;
  link_url: string;
  is_active: boolean;
}

function BannerModal({ isOpen, onClose, banner, onSuccess }: {
  isOpen: boolean;
  onClose: () => void;
  banner?: Banner;
  onSuccess: () => void;
}) {
  const isEdit = !!banner;
  const { loading, error, success, submit, reset } = useFormSubmit();
  const [uploadError, setUploadError] = useState<string | null>(null);
  
  const {
    values,
    errors,
    handleChange,
    setError,
    clearErrors,
    reset: resetForm,
    setValue
  } = useForm<BannerForm>({
    title: banner?.title || '',
    description: banner?.description || '',
    image_url: banner?.image_url || '',
    link_url: banner?.link_url || '',
    is_active: banner?.is_active ?? true
  });

  const validateForm = (): boolean => {
    clearErrors();
    let isValid = true;

    if (!values.title.trim()) {
      setError('title', 'Banner title is required');
      isValid = false;
    }

    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    const success = await submit(async () => {
      if (isEdit) {
        return bannerAPI.updateBanner(banner.id, values);
      } else {
        return bannerAPI.createBanner(values);
      }
    });

    if (success) {
      onSuccess();
      onClose();
      resetForm();
      reset();
    }
  };

  const handleClose = () => {
    onClose();
    resetForm();
    reset();
    setUploadError(null);
  };

  const handleImageUploaded = (imageUrl: string) => {
    setValue('image_url', imageUrl);
    setUploadError(null);
  };

  const handleImageError = (error: string) => {
    setUploadError(error);
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue('is_active', e.target.checked);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-4 mx-auto p-6 border w-full max-w-7xl shadow-lg rounded-md bg-white min-h-[90vh]">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-gray-900">
              {isEdit ? 'Edit Banner' : 'Create New Banner'}
            </h3>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Global Messages */}
          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="ml-3">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              </div>
            </div>
          )}

          {success && (
            <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
              <div className="flex">
                <svg className="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="ml-3">
                  <p className="text-sm text-green-800">{success}</p>
                </div>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            {/* Four Column Layout - Banner spans 3, Form spans 1 */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">

              {/* Column 1-3: Banner Image Preview & Upload */}
              <div className="lg:col-span-3">
                <div className="bg-gray-50 rounded-lg p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Banner Image (13:5 Aspect Ratio)</h4>
                  {uploadError && (
                    <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-3">
                      <p className="text-sm text-red-800">{uploadError}</p>
                    </div>
                  )}

                  {/* Banner Image Preview */}
                  {values.image_url && (
                    <div className="mb-4">
                      <div className="relative w-full bg-gray-100 rounded-lg border border-gray-300 overflow-hidden" style={{ aspectRatio: '13/5' }}>
                        <img
                          src={values.image_url}
                          alt="Banner preview"
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                          13:5 Banner Preview
                        </div>
                        <button
                          type="button"
                          onClick={() => setValue('image_url', '')}
                          className="absolute top-2 left-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  )}

                  <BannerImageUpload
                    onImageUploaded={handleImageUploaded}
                    onError={handleImageError}
                  />
                </div>
              </div>

              {/* Column 4: Banner Form */}
              <div className="lg:col-span-1">
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-6">Banner Details</h4>
                  <div className="space-y-6">

                    {/* Banner Title */}
                    <div>
                      <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                        Banner Title *
                      </label>
                      <input
                        type="text"
                        id="title"
                        name="title"
                        value={values.title}
                        onChange={handleChange}
                        className={getInputClassName(!!errors.title)}
                        placeholder="Enter banner title"
                      />
                      {errors.title && (
                        <p className="mt-1 text-sm text-red-600">{errors.title}</p>
                      )}
                    </div>

                    {/* Description */}
                    <div>
                      <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                        Description
                      </label>
                      <textarea
                        id="description"
                        name="description"
                        rows={3}
                        value={values.description}
                        onChange={handleChange}
                        className={getInputClassName()}
                        placeholder="Enter banner description (optional)"
                      />
                    </div>

                    {/* Link URL */}
                    <div>
                      <label htmlFor="link_url" className="block text-sm font-medium text-gray-700 mb-2">
                        Link URL
                      </label>
                      <input
                        type="url"
                        id="link_url"
                        name="link_url"
                        value={values.link_url}
                        onChange={handleChange}
                        className={getInputClassName()}
                        placeholder="https://example.com/page"
                      />
                    </div>

                    {/* Active Status */}
                    <div className="flex items-center">
                      <input
                        id="is_active"
                        name="is_active"
                        type="checkbox"
                        checked={values.is_active}
                        onChange={handleCheckboxChange}
                        className="h-4 w-4 text-[#E6B120] focus:ring-[#E6B120] border-gray-300 rounded"
                      />
                      <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                        Active (visible on website)
                      </label>
                    </div>

                  </div>
                </div>
              </div>

            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 pt-8 border-t border-gray-200 mt-8">
              <button
                type="button"
                onClick={handleClose}
                className="px-6 py-3 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120] transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className={`px-6 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white transition-colors ${
                  loading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-[#E6B120] hover:bg-[#FFCD29] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120]'
                }`}
              >
                {loading ? 'Saving...' : (isEdit ? 'Update Banner' : 'Create Banner')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

function BannersContent() {
  const { items: banners, loading, error, refetch, addItem, updateItem, removeItem } = useListData<Banner>(
    () => bannerAPI.getAllBanners()
  );
  const { isOpen, data: selectedBanner, open, close } = useModal();
  const [deleteLoading, setDeleteLoading] = useState<number | null>(null);

  const handleCreate = () => {
    open();
  };

  const handleEdit = (banner: Banner) => {
    open(banner);
  };

  const handleDelete = async (banner: Banner) => {
    if (!confirm(`Are you sure you want to delete "${banner.title}"?`)) return;

    setDeleteLoading(banner.id);
    try {
      const response = await bannerAPI.deleteBanner(banner.id);
      if (response.success) {
        removeItem(banner.id);
      } else {
        alert(response.error || 'Failed to delete banner');
      }
    } catch (error: any) {
      alert(error.response?.data?.error || 'Failed to delete banner');
    } finally {
      setDeleteLoading(null);
    }
  };

  const handleModalSuccess = () => {
    refetch();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#E6B120]"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Banners</h1>
          <p className="text-gray-600">Manage website banners and promotions</p>
        </div>
        <button
          onClick={handleCreate}
          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#E6B120] hover:bg-[#FFCD29] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120]"
        >
          <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Banner
        </button>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Banners List */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        {banners.length === 0 ? (
          <div className="p-6 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No banners</h3>
            <p className="mt-1 text-sm text-gray-500">Get started by creating a new banner.</p>
            <div className="mt-6">
              <button
                onClick={handleCreate}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#E6B120] hover:bg-[#FFCD29] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120]"
              >
                <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Banner
              </button>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {banners.map((banner) => (
              <div key={banner.id} className="p-6 flex items-center justify-between">
                <div className="flex items-center space-x-4 flex-1">
                  {banner.image_url && (
                    <div className="relative w-32 bg-gray-100 rounded-lg border border-gray-200 overflow-hidden" style={{ aspectRatio: '13/5' }}>
                      <img
                        src={banner.image_url}
                        alt={banner.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h3 className="text-lg font-medium text-gray-900">{banner.title}</h3>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      banner.is_active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {banner.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  {banner.description && (
                    <p className="text-sm text-gray-500 mt-1">
                      {truncateText(banner.description, 100)}
                    </p>
                  )}
                  <div className="flex items-center space-x-4 mt-2">
                    {banner.image_url && (
                      <span className="text-xs text-gray-400">
                        Has Image
                      </span>
                    )}
                    {banner.link_url && (
                      <span className="text-xs text-gray-400">
                        Has Link
                      </span>
                    )}
                      <span className="text-xs text-gray-400">
                        Created: {formatDate(banner.created_at)}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleEdit(banner)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120]"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDelete(banner)}
                    disabled={deleteLoading === banner.id}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    {deleteLoading === banner.id ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modal */}
      <BannerModal
        isOpen={isOpen}
        onClose={close}
        banner={selectedBanner}
        onSuccess={handleModalSuccess}
      />
    </div>
  );
}

export default function BannersPage() {
  return (
    <ProtectedRoute>
      <AdminLayout>
        <BannersContent />
      </AdminLayout>
    </ProtectedRoute>
  );
}
