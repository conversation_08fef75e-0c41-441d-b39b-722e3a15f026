'use client';

import React, { useState, useEffect } from 'react';
import QRCode from 'qrcode';
import { formatIDR } from '@/lib/utils';

interface ProductQRSectionProps {
  productData: {
    name: string;
    price: string;
    brand_name?: string;
    category_name?: string;
    description?: string;
  };
  className?: string;
}

export default function ProductQRSection({ productData, className = '' }: ProductQRSectionProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);

  // Generate QR code data string
  const generateQRData = () => {
    const price = productData.price ? parseInt(productData.price) : 0;
    const formattedPrice = price > 0 ? formatIDR(price) : 'Price not set';
    
    return [
      `Product: ${productData.name || 'Unnamed Product'}`,
      `Price: ${formattedPrice}`,
      productData.brand_name ? `Brand: ${productData.brand_name}` : '',
      productData.category_name ? `Category: ${productData.category_name}` : '',
      productData.description ? `Description: ${productData.description.substring(0, 100)}${productData.description.length > 100 ? '...' : ''}` : ''
    ].filter(Boolean).join('\n');
  };

  // Generate QR code
  const generateQRCode = async () => {
    if (!productData.name) return;
    
    setIsGenerating(true);
    try {
      const qrData = generateQRData();
      const url = await QRCode.toDataURL(qrData, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        errorCorrectionLevel: 'M'
      });
      setQrCodeUrl(url);
    } catch (error) {
      console.error('Error generating QR code:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Auto-generate QR code when product data changes
  useEffect(() => {
    if (productData.name) {
      generateQRCode();
    } else {
      setQrCodeUrl('');
    }
  }, [productData.name, productData.price, productData.brand_name, productData.category_name, productData.description]);

  const downloadQRCode = () => {
    if (!qrCodeUrl) return;
    
    const link = document.createElement('a');
    link.download = `qr_${productData.name?.replace(/[^a-zA-Z0-9]/g, '_') || 'product'}.png`;
    link.href = qrCodeUrl;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const copyQRData = async () => {
    try {
      const qrData = generateQRData();
      await navigator.clipboard.writeText(qrData);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy QR data:', error);
    }
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">QR Code</h3>
      
      {/* QR Code Display */}
      <div className="text-center mb-4">
        {isGenerating ? (
          <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#E6B120] mx-auto mb-2"></div>
              <p className="text-sm text-gray-500">Generating QR Code...</p>
            </div>
          </div>
        ) : qrCodeUrl ? (
          <div className="bg-gray-50 rounded-lg p-4">
            <img
              src={qrCodeUrl}
              alt="Product QR Code"
              className="mx-auto rounded-lg shadow-sm"
              style={{ maxWidth: '100%', height: 'auto' }}
            />
          </div>
        ) : (
          <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
                </svg>
              </div>
              <p className="text-sm text-gray-500">QR Code will appear here</p>
              <p className="text-xs text-gray-400">Fill in product details to generate</p>
            </div>
          </div>
        )}
      </div>

      {/* QR Code Actions */}
      {qrCodeUrl && (
        <div className="space-y-3">
          <div className="flex space-x-2">
            <button
              onClick={downloadQRCode}
              className="flex-1 bg-[#E6B120] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#FFCD29] transition-colors"
            >
              Download QR
            </button>
            <button
              onClick={copyQRData}
              className="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors"
            >
              Copy Data
            </button>
          </div>
          
          <button
            onClick={generateQRCode}
            disabled={isGenerating}
            className="w-full bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors disabled:opacity-50"
          >
            {isGenerating ? 'Generating...' : 'Regenerate QR'}
          </button>
        </div>
      )}

      {/* QR Data Preview */}
      {productData.name && (
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-2">QR Code Data:</h4>
          <div className="text-xs text-gray-600 space-y-1">
            <div><strong>Product:</strong> {productData.name}</div>
            {productData.price && (
              <div><strong>Price:</strong> {formatIDR(parseInt(productData.price))}</div>
            )}
            {productData.brand_name && (
              <div><strong>Brand:</strong> {productData.brand_name}</div>
            )}
            {productData.category_name && (
              <div><strong>Category:</strong> {productData.category_name}</div>
            )}
            {productData.description && (
              <div><strong>Description:</strong> {productData.description.substring(0, 50)}{productData.description.length > 50 ? '...' : ''}</div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
