const rateLimit = require('express-rate-limit');

// Store for tracking suspicious behavior
const suspiciousIPs = new Map();
const failedAttempts = new Map();

// Helper function to check if IP is suspicious
const isSuspiciousIP = (ip) => {
  const suspiciousData = suspiciousIPs.get(ip);
  if (!suspiciousData) return false;

  // Check if IP is still in suspicious period
  if (Date.now() - suspiciousData.timestamp > 30 * 60 * 1000) { // 30 minutes
    suspiciousIPs.delete(ip);
    return false;
  }

  return suspiciousData.count >= 3; // Mark as suspicious after 3 failed attempts
};

// Helper function to mark IP as suspicious
const markSuspicious = (ip) => {
  const current = suspiciousIPs.get(ip) || { count: 0, timestamp: Date.now() };
  current.count++;
  current.timestamp = Date.now();
  suspiciousIPs.set(ip, current);
};

// Custom skip function for authenticated admin requests
const skipAuthenticatedAdmin = (req) => {
  // Skip rate limiting for authenticated admin sessions
  return req.session && req.session.admin && req.session.admin.id;
};

// Dynamic rate limiter that adjusts based on behavior
const createDynamicLimiter = (baseMax, windowMs, strictMax = null) => {
  return rateLimit({
    windowMs,
    max: (req) => {
      const ip = req.ip;

      // If authenticated admin, use higher limits
      if (skipAuthenticatedAdmin(req)) {
        return baseMax * 10; // 10x higher limit for authenticated admins
      }

      // If IP is suspicious, use strict limits
      if (isSuspiciousIP(ip)) {
        return strictMax || Math.floor(baseMax * 0.1); // 10% of base limit
      }

      return baseMax;
    },
    skip: (req) => {
      // Skip if explicitly marked to skip rate limiting
      if (req.skipRateLimit) {
        return true;
      }

      // Skip rate limiting for authenticated admin requests (except login)
      if (skipAuthenticatedAdmin(req) && !req.path.includes('/login')) {
        return true;
      }

      // Define safe methods and paths that should not be rate limited
      const safeMethods = ['GET'];
      const safePaths = [
        '/api/health',           // Health check endpoint
        '/api/admin/check',      // Admin auth check
        '/api/brands',           // Public brand listings
        '/api/categories',       // Public category listings
        '/api/products',         // Public product listings
        '/api/banners',          // Public banner listings
        '/api/banners/active',   // Active banners only
        '/uploads'               // Static image files
      ];

      // Skip rate limiting for safe GET requests to public endpoints
      if (safeMethods.includes(req.method) && safePaths.some(p => req.path.startsWith(p))) {
        return true;
      }

      return false;
    },
    handler: (req, res) => {
      markSuspicious(req.ip);
      console.log(`Rate limit exceeded for IP: ${req.ip} on ${req.path}`);
      res.status(429).json({
        success: false,
        error: 'Too many requests, please try again later.',
        retryAfter: Math.round(req.rateLimit.resetTime / 1000)
      });
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
};

// General API rate limiter - more permissive for normal usage
const generalLimiter = createDynamicLimiter(500, 15 * 60 * 1000, 50);

// Strict rate limiter for write operations - higher limits for admins
const strictLimiter = createDynamicLimiter(200, 15 * 60 * 1000, 20);

// Admin operations limiter - very high limits for authenticated admins
const adminLimiter = createDynamicLimiter(1000, 15 * 60 * 1000, 100);

// Rate limiter for rating submissions to prevent spam
const ratingLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // Increased to 10 rating submissions per hour
  message: {
    success: false,
    error: 'Too many rating submissions from this IP, please try again later.',
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiter for admin login attempts - strict to prevent brute force
const adminLoginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: (req) => {
    const ip = req.ip;
    // If IP has been marked as suspicious, allow only 2 attempts
    if (isSuspiciousIP(ip)) {
      return 2;
    }
    return 10; // Normal limit increased to 10 attempts
  },
  message: {
    success: false,
    error: 'Too many login attempts from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Don't count successful requests
  handler: (req, res) => {
    markSuspicious(req.ip);
    res.status(429).json({
      success: false,
      error: 'Too many login attempts from this IP, please try again later.',
      retryAfter: '15 minutes'
    });
  },
});

// Rate limiter for database test endpoint
const testLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 20, // Increased to 20 test requests per 5 minutes
  message: {
    success: false,
    error: 'Too many test requests from this IP, please try again later.',
    retryAfter: '5 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Cleanup function to prevent memory leaks
const cleanupSuspiciousIPs = () => {
  const now = Date.now();
  for (const [ip, data] of suspiciousIPs.entries()) {
    if (now - data.timestamp > 60 * 60 * 1000) { // 1 hour
      suspiciousIPs.delete(ip);
    }
  }
  for (const [ip, data] of failedAttempts.entries()) {
    if (now - data.timestamp > 60 * 60 * 1000) { // 1 hour
      failedAttempts.delete(ip);
    }
  }
};

// Run cleanup every 30 minutes
setInterval(cleanupSuspiciousIPs, 30 * 60 * 1000);

module.exports = {
  generalLimiter,
  strictLimiter,
  adminLimiter,
  adminLoginLimiter,
  ratingLimiter,
  testLimiter,
  cleanupSuspiciousIPs
};
