'use client';

import React, { useState, useEffect } from 'react';

interface ImageData {
  id?: number;
  url: string;
  file?: File;
  isNew?: boolean;
}

interface ProductImageCarouselProps {
  images: ImageData[];
  onImagesChange: (images: ImageData[]) => void;
  onError?: (error: string) => void;
  maxImages?: number;
  className?: string;
  onDeleteImage?: (imageId: number) => Promise<void>;
}

export default function ProductImageCarousel({
  images,
  onImagesChange,
  onError,
  maxImages = 10,
  className = '',
  onDeleteImage
}: ProductImageCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  // Reset current index when images change to prevent out of bounds
  useEffect(() => {
    if (images.length === 0) {
      setCurrentIndex(0);
    } else if (currentIndex >= images.length) {
      setCurrentIndex(Math.max(0, images.length - 1));
    }
  }, [images.length, currentIndex]);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    if (images.length >= maxImages) {
      onError?.(`Maximum ${maxImages} images allowed.`);
      return;
    }

    const file = files[0];
    
    // Validate file type
    if (!file.type.startsWith('image/')) {
      onError?.('Please select an image file.');
      return;
    }

    // Validate file size (10MB)
    if (file.size > 10 * 1024 * 1024) {
      onError?.('File size must be less than 10MB.');
      return;
    }

    setIsUploading(true);

    // Create preview URL
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        const newImage: ImageData = {
          url: e.target.result as string,
          file: file,
          isNew: true
        };
        
        const updatedImages = [...images, newImage];
        onImagesChange(updatedImages);
        setCurrentIndex(updatedImages.length - 1);
      }
      setIsUploading(false);
    };
    reader.readAsDataURL(file);
  };

  const handleRemoveImage = async (index: number) => {
    const imageToRemove = images[index];

    try {
      // If it's an existing image (has ID), delete from database
      if (imageToRemove.id && onDeleteImage) {
        await onDeleteImage(imageToRemove.id);
      }

      const updatedImages = images.filter((_, i) => i !== index);
      onImagesChange(updatedImages);

      // Adjust current index if necessary
      if (currentIndex >= updatedImages.length) {
        setCurrentIndex(Math.max(0, updatedImages.length - 1));
      }
    } catch (error) {
      onError?.('Failed to delete image');
    }
  };

  const goToPrevious = () => {
    if (images.length === 0) return;
    setCurrentIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  };

  const goToNext = () => {
    if (images.length === 0) return;
    setCurrentIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
  };

  const goToSlide = (index: number) => {
    if (index < 0 || index >= images.length) return;
    setCurrentIndex(index);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Image Display */}
      <div className="relative bg-gray-100 rounded-lg overflow-hidden" style={{ aspectRatio: '4/3', minHeight: '300px' }}>
        {images.length > 0 ? (
          <>
            <img
              src={images[currentIndex].url}
              alt={`Product image ${currentIndex + 1}`}
              className="w-full h-full object-cover"
            />
            
            {/* Navigation Arrows */}
            {images.length > 1 && (
              <>
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    goToPrevious();
                  }}
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    goToNext();
                  }}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </>
            )}

            {/* Remove Current Image Button */}
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleRemoveImage(currentIndex);
              }}
              className="absolute top-2 right-2 bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-all"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* Image Counter */}
            <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
              {currentIndex + 1} / {images.length}
            </div>

            {/* New Image Badge */}
            {images[currentIndex].isNew && (
              <div className="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded text-sm">
                New
              </div>
            )}
          </>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-400">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <p className="text-sm">No images uploaded</p>
              <p className="text-xs">Click the + button to add images</p>
            </div>
          </div>
        )}
      </div>

      {/* Thumbnail Navigation */}
      <div className="flex items-center space-x-2">
        {/* Thumbnails */}
        <div className="flex space-x-2 flex-1 overflow-x-auto">
          {images.map((image, index) => (
            <button
              key={index}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                goToSlide(index);
              }}
              className={`relative flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ${
                index === currentIndex
                  ? 'border-[#E6B120] ring-2 ring-[#E6B120] ring-opacity-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <img
                src={image.url}
                alt={`Thumbnail ${index + 1}`}
                className="w-full h-full object-cover"
              />
              {image.isNew && (
                <div className="absolute top-0 right-0 w-2 h-2 bg-green-500 rounded-full"></div>
              )}
            </button>
          ))}
        </div>

        {/* Add Image Button */}
        {images.length < maxImages && (
          <label className="flex-shrink-0 w-16 h-16 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center cursor-pointer hover:border-[#E6B120] hover:bg-[#E6B120] hover:bg-opacity-5 transition-all">
            <input
              type="file"
              accept="image/*"
              onChange={handleFileSelect}
              className="hidden"
              disabled={isUploading}
            />
            {isUploading ? (
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#E6B120]"></div>
            ) : (
              <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            )}
          </label>
        )}
      </div>

      {/* Image Count Info */}
      <div className="text-sm text-gray-500 text-center">
        {images.length} of {maxImages} images uploaded
      </div>
    </div>
  );
}
