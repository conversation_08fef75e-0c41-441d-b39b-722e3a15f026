'use client';

import React, { useState, useEffect } from 'react';
import { formatPriceInput, parsePriceInput, formatIDR, getInputClassName } from '@/lib/utils';

interface PriceInputProps {
  value: string;
  onChange: (value: string) => void;
  error?: string;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  required?: boolean;
}

export default function PriceInput({
  value,
  onChange,
  error,
  placeholder = "0",
  className = "",
  disabled = false,
  required = false
}: PriceInputProps) {
  const [displayValue, setDisplayValue] = useState('');
  const [isFocused, setIsFocused] = useState(false);

  // Update display value when value prop changes
  useEffect(() => {
    if (value) {
      setDisplayValue(formatPriceInput(value));
    } else {
      setDisplayValue('');
    }
  }, [value]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    
    // Remove all non-numeric characters
    const numericValue = inputValue.replace(/[^\d]/g, '');
    
    // Update the actual value (numeric string)
    onChange(numericValue);
    
    // Update display value (formatted)
    if (numericValue) {
      setDisplayValue(formatPriceInput(numericValue));
    } else {
      setDisplayValue('');
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  // Show preview of formatted currency
  const previewValue = value ? formatIDR(parseInt(value)) : '';

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <span className="text-gray-500 text-sm">Rp</span>
        </div>
        <input
          type="text"
          value={displayValue}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          className={`${getInputClassName(!!error)} pl-10 pr-4`}
        />
      </div>
      
      {/* Preview of formatted currency */}
      {previewValue && !isFocused && (
        <div className="text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-md border">
          <span className="font-medium">Preview:</span> {previewValue}
        </div>
      )}
      
      {/* Error message */}
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
      
      {/* Helper text */}
      {!error && (
        <p className="text-xs text-gray-500">
          Masukkan harga dalam bentuk angka. Format IDR akan otomatis di terapkan.
        </p>
      )}
    </div>
  );
}
