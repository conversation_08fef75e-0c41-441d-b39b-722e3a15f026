import axios from 'axios';

// API base URL - adjust based on your environment
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  withCredentials: true, // Important for session cookies
  headers: {
    'Content-Type': 'application/json',
  },
});

// Response interceptor for handling common errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Don't automatically redirect on 401 errors
    // Let individual components handle authentication errors appropriately
    return Promise.reject(error);
  }
);

// Types for API responses
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Admin Authentication API
export const adminAuth = {
  login: async (username: string, password: string): Promise<ApiResponse> => {
    const response = await apiClient.post('/admin/login', { username, password });
    return response.data;
  },

  logout: async (): Promise<ApiResponse> => {
    const response = await apiClient.post('/admin/logout');
    return response.data;
  },

  checkAuth: async (): Promise<ApiResponse> => {
    const response = await apiClient.get('/admin/check');
    return response.data;
  },
};

// Admin Management API
export const adminAPI = {
  getAllAdmins: async (): Promise<ApiResponse> => {
    const response = await apiClient.get('/admins');
    return response.data;
  },
  
  createAdmin: async (adminData: { username: string; password: string }): Promise<ApiResponse> => {
    const response = await apiClient.post('/admins', adminData);
    return response.data;
  },
};

// Brand Management API
export const brandAPI = {
  getAllBrands: async (): Promise<ApiResponse> => {
    const response = await apiClient.get('/brands');
    return response.data;
  },
  
  getBrandById: async (id: number): Promise<ApiResponse> => {
    const response = await apiClient.get(`/brands/${id}`);
    return response.data;
  },
  
  createBrand: async (brandData: { name: string; description?: string }): Promise<ApiResponse> => {
    const response = await apiClient.post('/brands', brandData);
    return response.data;
  },
  
  updateBrand: async (id: number, brandData: { name: string; description?: string }): Promise<ApiResponse> => {
    const response = await apiClient.put(`/brands/${id}`, brandData);
    return response.data;
  },
  
  deleteBrand: async (id: number): Promise<ApiResponse> => {
    const response = await apiClient.delete(`/brands/${id}`);
    return response.data;
  },
};

// Category Management API
export const categoryAPI = {
  getAllCategories: async (): Promise<ApiResponse> => {
    const response = await apiClient.get('/categories');
    return response.data;
  },
  
  getCategoryById: async (id: number): Promise<ApiResponse> => {
    const response = await apiClient.get(`/categories/${id}`);
    return response.data;
  },
  
  createCategory: async (categoryData: { name: string; description?: string }): Promise<ApiResponse> => {
    const response = await apiClient.post('/categories', categoryData);
    return response.data;
  },
  
  updateCategory: async (id: number, categoryData: { name: string; description?: string }): Promise<ApiResponse> => {
    const response = await apiClient.put(`/categories/${id}`, categoryData);
    return response.data;
  },
  
  deleteCategory: async (id: number): Promise<ApiResponse> => {
    const response = await apiClient.delete(`/categories/${id}`);
    return response.data;
  },
};

// Product Management API
export const productAPI = {
  getAllProducts: async (): Promise<ApiResponse> => {
    const response = await apiClient.get('/products');
    return response.data;
  },
  
  getProductById: async (id: number): Promise<ApiResponse> => {
    const response = await apiClient.get(`/products/${id}`);
    return response.data;
  },
  
  createProduct: async (productData: any): Promise<ApiResponse> => {
    const response = await apiClient.post('/products', productData);
    return response.data;
  },
  
  updateProduct: async (id: number, productData: any): Promise<ApiResponse> => {
    const response = await apiClient.put(`/products/${id}`, productData);
    return response.data;
  },
  
  deleteProduct: async (id: number): Promise<ApiResponse> => {
    const response = await apiClient.delete(`/products/${id}`);
    return response.data;
  },
};

// Product Variant API
export const variantAPI = {
  getVariantsByProductId: async (productId: number): Promise<ApiResponse> => {
    const response = await apiClient.get(`/products/${productId}/variants`);
    return response.data;
  },
  
  createVariant: async (productId: number, variantData: any): Promise<ApiResponse> => {
    const response = await apiClient.post(`/products/${productId}/variants`, variantData);
    return response.data;
  },
  
  updateVariant: async (id: number, variantData: any): Promise<ApiResponse> => {
    const response = await apiClient.put(`/variants/${id}`, variantData);
    return response.data;
  },
  
  deleteVariant: async (id: number): Promise<ApiResponse> => {
    const response = await apiClient.delete(`/variants/${id}`);
    return response.data;
  },
};

// Product Photo API
export const photoAPI = {
  getPhotosByProductId: async (productId: number): Promise<ApiResponse> => {
    const response = await apiClient.get(`/products/${productId}/photos`);
    return response.data;
  },

  createPhoto: async (productId: number, photoData: { photo_url: string }): Promise<ApiResponse> => {
    const response = await apiClient.post(`/products/${productId}/photos`, photoData);
    return response.data;
  },

  deletePhoto: async (id: number): Promise<ApiResponse> => {
    const response = await apiClient.delete(`/photos/${id}`);
    return response.data;
  },
};

// Image Upload API
export const uploadAPI = {
  uploadSingleImage: async (imageFile: File): Promise<ApiResponse> => {
    const formData = new FormData();
    formData.append('image', imageFile);
    const response = await apiClient.post('/upload/product-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  uploadMultipleImages: async (imageFiles: File[]): Promise<ApiResponse> => {
    const formData = new FormData();
    imageFiles.forEach(file => {
      formData.append('images', file);
    });
    const response = await apiClient.post('/upload/product-images', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
};

// Banner Management API
export const bannerAPI = {
  getAllBanners: async (): Promise<ApiResponse> => {
    const response = await apiClient.get('/banners');
    return response.data;
  },
  
  getActiveBanners: async (): Promise<ApiResponse> => {
    const response = await apiClient.get('/banners/active');
    return response.data;
  },
  
  createBanner: async (bannerData: any): Promise<ApiResponse> => {
    const response = await apiClient.post('/banners', bannerData);
    return response.data;
  },
  
  updateBanner: async (id: number, bannerData: any): Promise<ApiResponse> => {
    const response = await apiClient.put(`/banners/${id}`, bannerData);
    return response.data;
  },
  
  deleteBanner: async (id: number): Promise<ApiResponse> => {
    const response = await apiClient.delete(`/banners/${id}`);
    return response.data;
  },
};

// Rating API (for viewing ratings in admin)
export const ratingAPI = {
  getRatingsByProductId: async (productId: number): Promise<ApiResponse> => {
    const response = await apiClient.get(`/products/${productId}/ratings`);
    return response.data;
  },
};

// Health check API
export const healthAPI = {
  checkHealth: async (): Promise<ApiResponse> => {
    const response = await apiClient.get('/health');
    return response.data;
  },
  
  testDatabase: async (): Promise<ApiResponse> => {
    const response = await apiClient.get('/test-db');
    return response.data;
  },
};

export default apiClient;
