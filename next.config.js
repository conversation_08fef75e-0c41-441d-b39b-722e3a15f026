/** @type {import('next').NextConfig} */
const nextConfig = {
  // Improve development server stability
  experimental: {
    // Turbopack can be enabled/disabled via CLI flag
  },

  // Configure webpack for better performance in development
  webpack: (config, { dev }) => {
    if (dev) {
      // Reduce memory usage and improve stability
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
        ignored: ['**/node_modules/**', '**/.git/**', '**/API/**'],
      };
    }
    return config;
  },

  // Configure images
  images: {
    domains: ['localhost'],
    formats: ['image/webp', 'image/avif'],
  },

  // Configure headers for better performance
  async headers() {
    return [
      {
        source: '/uploads/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
