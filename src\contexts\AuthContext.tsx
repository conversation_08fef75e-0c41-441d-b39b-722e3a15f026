'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { adminAuth } from '@/lib/api';
import { useRouter } from 'next/navigation';

// Types
interface Admin {
  id: number;
  username: string;
}

interface AuthContextType {
  admin: Admin | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [admin, setAdmin] = useState<Admin | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastAuthCheck, setLastAuthCheck] = useState<number>(0);
  const router = useRouter();

  const isAuthenticated = admin !== null;

  // Check authentication status on mount and when needed
  const checkAuth = async (force: boolean = false) => {
    // Throttle auth checks to prevent rate limiting (minimum 5 seconds between checks)
    const now = Date.now();
    if (!force && now - lastAuthCheck < 5000) {
      console.log('Auth check throttled, using cached state');
      return;
    }

    try {
      setIsLoading(true);
      setLastAuthCheck(now);

      // Use the dedicated auth check endpoint
      const response = await adminAuth.checkAuth();

      if (response.success && response.data) {
        // Set admin data from server response
        const adminData = {
          id: response.data.id,
          username: response.data.username
        };
        setAdmin(adminData);
        localStorage.setItem('admin', JSON.stringify(adminData));
      } else {
        // Not authenticated
        setAdmin(null);
        localStorage.removeItem('admin');
      }
    } catch (error: any) {
      console.error('Auth check failed:', error);
      // If it's a 401 error, we're definitely not authenticated
      if (error.response?.status === 401) {
        setAdmin(null);
        localStorage.removeItem('admin');
      } else if (error.response?.status === 429) {
        // Rate limited - use stored admin data if available
        console.warn('Auth check rate limited, using stored data');
        const storedAdmin = localStorage.getItem('admin');
        if (storedAdmin) {
          try {
            setAdmin(JSON.parse(storedAdmin));
          } catch {
            setAdmin(null);
            localStorage.removeItem('admin');
          }
        }
      } else {
        // For other errors, try to use stored admin data if available
        const storedAdmin = localStorage.getItem('admin');
        if (storedAdmin) {
          try {
            setAdmin(JSON.parse(storedAdmin));
          } catch {
            setAdmin(null);
            localStorage.removeItem('admin');
          }
        } else {
          setAdmin(null);
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Login function
  const login = async (username: string, password: string) => {
    try {
      setIsLoading(true);
      const response = await adminAuth.login(username, password);

      if (response.success) {
        // After successful login, get the admin data from the server
        await checkAuth(true);
        return { success: true };
      } else {
        return { success: false, error: response.error || 'Login failed' };
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Network error'
      };
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setIsLoading(true);
      await adminAuth.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setAdmin(null);
      localStorage.removeItem('admin');
      setIsLoading(false);
      router.push('/login');
    }
  };

  // Check auth on mount
  useEffect(() => {
    // Check if we're on the login page - don't do auth checks there
    if (typeof window !== 'undefined' && window.location.pathname === '/login') {
      // On login page, check if user has ever logged in before (has stored admin data)
      const storedAdmin = localStorage.getItem('admin');
      if (storedAdmin) {
        try {
          JSON.parse(storedAdmin); // Just validate the stored data is valid JSON
          // If we have stored admin data, verify it's still valid
          setIsLoading(true);
          setTimeout(() => checkAuth(true), 100);
        } catch {
          // Invalid stored data, clear it and show login form
          localStorage.removeItem('admin');
          setIsLoading(false);
        }
      } else {
        // No stored admin data, user has never logged in - show login form
        setIsLoading(false);
      }
      return;
    }

    // For non-login pages, do the normal auth check
    const storedAdmin = localStorage.getItem('admin');
    if (storedAdmin) {
      try {
        const adminData = JSON.parse(storedAdmin);
        setAdmin(adminData);
        setIsLoading(false);
        // Then verify with server in background
        setTimeout(() => checkAuth(true), 100);
      } catch {
        localStorage.removeItem('admin');
        checkAuth(true);
      }
    } else {
      checkAuth(true);
    }
  }, []);

  const value: AuthContextType = {
    admin,
    isAuthenticated,
    isLoading,
    login,
    logout,
    checkAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// HOC for protected routes
export function withAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isLoading } = useAuth();
    const router = useRouter();

    useEffect(() => {
      if (!isLoading && !isAuthenticated) {
        router.push('/login');
      }
    }, [isAuthenticated, isLoading, router]);

    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-white">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#E6B120] mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
      );
    }

    if (!isAuthenticated) {
      return null; // Will redirect to login
    }

    return <Component {...props} />;
  };
}

// Component for protecting routes
interface ProtectedRouteProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export function ProtectedRoute({ children, fallback }: ProtectedRouteProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center bg-white">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#E6B120] mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
      )
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  return <>{children}</>;
}

export default AuthContext;
