<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        nav {
            display: flex;
            flex-direction: row;
            font-family: Istok Web, sans-serif;
            justify-content: space-between;
            align-items: center;
            background-color: #000000;
            width: 100%;
            padding: 20px 100px;
        }
        ul {
            list-style: none;
            display: flex;
            flex-direction: row;
            gap: 50px;
        }
        li a {
            color: #838383;
            text-decoration: none;
        }
        li .navLinkActive {
            color: #e6b120;
        }
        .searchBar {
            display: flex;
            align-items: center;
            border-radius: 5px;
            width: 200px;
        }
        .searchBar input {
            border: none;
            border-radius: 5px 0 0 5px;
            outline: none;
            background: #323232;
            color: #ffffff;
            padding: 10px 20px;
        }
        .searchBar i {
            color: #000000;
            background: #ffcd29;
            padding: 10px 10px;
            border-radius: 0 5px 5px 0;
        }
    </style>
</head>
<body>
    <nav>
        <div class="logo">
            <a href="">
                <img src="/public/assets/logoCatalog.png" alt="Logo Catalog" width="40px">
            </a>
        </div>
        <div class="navLinks">
            <ul>
                <li><a class="navLinkActive" href="">Home</a></li>
                <li><a class="" href="">Katalog</a></li>
                <li><a class="" href="">Brands</a></li>
                <li><a class="" href="">Wishlist</a></li>
            </ul>
        </div>
        <div class="searchBar">
            <input type="text" placeholder="Cari product...">
            <a href="#">
                <i class="fa fa-search"></i>
            </a>
        </div>
    </nav>
    
</body>
</html>