'use client';

import React from 'react';
import Link from 'next/link';
import { formatDate } from '@/lib/utils';

interface DashboardCardProps {
  title: string;
  items: any[];
  loading: boolean;
  emptyMessage: string;
  viewAllHref: string;
  createHref: string;
  renderItem: (item: any) => React.ReactNode;
  emptyIcon: React.ReactNode;
}

export default function DashboardCard({
  title,
  items,
  loading,
  emptyMessage,
  viewAllHref,
  createHref,
  renderItem,
  emptyIcon
}: DashboardCardProps) {
  return (
    <div>
      <h2 className="text-lg font-medium text-gray-900 mb-4">{title}</h2>
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        {loading ? (
          <div className="p-6">
            <div className="animate-pulse space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : items && items.length > 0 ? (
          <div className="divide-y divide-gray-200">
            {items.slice(0, 5).map((item) => (
              <div key={item.id} className="p-6">
                {renderItem(item)}
              </div>
            ))}
            <div className="px-6 py-3 bg-gray-50">
              <Link
                href={viewAllHref}
                className="text-sm font-medium text-[#E6B120] hover:text-[#FFCD29] transition-colors duration-200"
              >
                View all {title.toLowerCase()} →
              </Link>
            </div>
          </div>
        ) : (
          <div className="p-6 text-center">
            {emptyIcon}
            <h3 className="mt-2 text-sm font-medium text-gray-900">No {title.toLowerCase()}</h3>
            <p className="mt-1 text-sm text-gray-500">{emptyMessage}</p>
            <div className="mt-6">
              <Link
                href={createHref}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#E6B120] hover:bg-[#FFCD29] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E6B120] transition-colors duration-200"
              >
                <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Create {title.slice(0, -1)}
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Specific item renderers
export const ProductItemRenderer = (product: any) => (
  <div className="flex items-center justify-between">
    <div className="flex items-center space-x-4">
      {product.photos && product.photos.length > 0 ? (
        <img 
          src={product.photos[0].photo_url} 
          alt={product.name}
          className="w-12 h-12 object-cover rounded-lg border border-gray-200"
        />
      ) : (
        <div className="w-12 h-12 bg-[#E6B120] rounded-lg flex items-center justify-center">
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
          </svg>
        </div>
      )}
      <div>
        <h3 className="text-sm font-medium text-gray-900">{product.name}</h3>
        <p className="text-sm text-gray-500">
          {product.brand_name} • {product.category_name}
        </p>
      </div>
    </div>
    <div className="text-right">
      <p className="text-sm text-gray-500">
        {formatDate(product.created_at)}
      </p>
    </div>
  </div>
);

export const BrandItemRenderer = (brand: any) => (
  <div className="flex items-center justify-between">
    <div className="flex items-center space-x-4">
      {brand.brand_photo ? (
        <img 
          src={brand.brand_photo} 
          alt={brand.name}
          className="w-12 h-12 object-cover rounded-lg border border-gray-200"
        />
      ) : (
        <div className="w-12 h-12 bg-[#FFCD29] rounded-lg flex items-center justify-center">
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
          </svg>
        </div>
      )}
      <div>
        <h3 className="text-sm font-medium text-gray-900">{brand.name}</h3>
        <p className="text-sm text-gray-500">
          {brand.description ? brand.description.substring(0, 50) + '...' : 'No description'}
        </p>
      </div>
    </div>
    <div className="text-right">
      <p className="text-sm text-gray-500">
        {formatDate(brand.created_at)}
      </p>
    </div>
  </div>
);

export const CategoryItemRenderer = (category: any) => (
  <div className="flex items-center justify-between">
    <div className="flex items-center space-x-4">
      {category.category_photo ? (
        <img 
          src={category.category_photo} 
          alt={category.name}
          className="w-12 h-12 object-cover rounded-lg border border-gray-200"
        />
      ) : (
        <div className="w-12 h-12 bg-gray-600 rounded-lg flex items-center justify-center">
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </div>
      )}
      <div>
        <h3 className="text-sm font-medium text-gray-900">{category.name}</h3>
        <p className="text-sm text-gray-500">
          {category.description ? category.description.substring(0, 50) + '...' : 'No description'}
        </p>
      </div>
    </div>
    <div className="text-right">
      <p className="text-sm text-gray-500">
        {formatDate(category.created_at)}
      </p>
    </div>
  </div>
);

export const BannerItemRenderer = (banner: any) => (
  <div className="flex items-center justify-between">
    <div className="flex items-center space-x-4">
      {banner.image_url ? (
        <img 
          src={banner.image_url} 
          alt={banner.title}
          className="w-16 h-10 object-cover rounded-lg border border-gray-200"
        />
      ) : (
        <div className="w-16 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>
      )}
      <div>
        <h3 className="text-sm font-medium text-gray-900">{banner.title}</h3>
        <div className="flex items-center space-x-2">
          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
            banner.is_active 
              ? 'bg-green-100 text-green-800' 
              : 'bg-gray-100 text-gray-800'
          }`}>
            {banner.is_active ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>
    </div>
    <div className="text-right">
      <p className="text-sm text-gray-500">
        {formatDate(banner.created_at)}
      </p>
    </div>
  </div>
);
