const multer = require('multer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs').promises;

// Create uploads directory if it doesn't exist
const createUploadsDir = async () => {
  // Point to NextJS public/uploads directory
  const uploadsDir = path.join(__dirname, '../../public/uploads');
  try {
    await fs.access(uploadsDir);
  } catch (error) {
    await fs.mkdir(uploadsDir, { recursive: true });
  }
  return uploadsDir;
};

// Configure multer for memory storage
const storage = multer.memoryStorage();

const fileFilter = (req, file, cb) => {
  // Check if file is an image
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed!'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
});

// Process and save image
const processImage = async (buffer, filename) => {
  try {
    const uploadsDir = await createUploadsDir();
    
    // Get image metadata
    const metadata = await sharp(buffer).metadata();
    
    // Calculate crop dimensions for 1:1 aspect ratio
    const size = Math.min(metadata.width, metadata.height);
    const left = Math.floor((metadata.width - size) / 2);
    const top = Math.floor((metadata.height - size) / 2);
    
    // Process image: crop to 1:1, resize to 800x800, convert to WebP
    const processedBuffer = await sharp(buffer)
      .extract({ left, top, width: size, height: size })
      .resize(800, 800, {
        kernel: sharp.kernel.lanczos3,
        fit: 'cover',
        position: 'center'
      })
      .webp({ quality: 85 })
      .toBuffer();
    
    // Generate filename with WebP extension
    const webpFilename = filename.includes('.')
      ? filename.replace(/\.[^/.]+$/, '.webp')
      : `${filename}.webp`;
    const filepath = path.join(uploadsDir, webpFilename);
    
    // Save processed image
    await fs.writeFile(filepath, processedBuffer);
    
    return {
      filename: webpFilename,
      path: filepath,
      size: processedBuffer.length,
      dimensions: { width: 800, height: 800 }
    };
  } catch (error) {
    throw new Error(`Image processing failed: ${error.message}`);
  }
};

// Middleware for single image upload
const uploadSingle = (fieldName) => {
  return async (req, res, next) => {
    const uploadMiddleware = upload.single(fieldName);
    
    uploadMiddleware(req, res, async (err) => {
      if (err) {
        if (err instanceof multer.MulterError) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
              success: false,
              error: 'File too large. Maximum size is 10MB.'
            });
          }
        }
        return res.status(400).json({
          success: false,
          error: err.message
        });
      }
      
      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: 'No image file provided.'
        });
      }
      
      try {
        // Generate unique filename
        const timestamp = Date.now();
        const randomString = Math.random().toString(36).substring(2, 15);
        const filename = `${timestamp}_${randomString}`;
        
        // Process the image
        const result = await processImage(req.file.buffer, filename);
        
        // Add processed image info to request
        req.processedImage = result;
        
        next();
      } catch (error) {
        return res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });
  };
};

// Middleware for multiple image upload
const uploadMultiple = (fieldName, maxCount = 5) => {
  return async (req, res, next) => {
    const uploadMiddleware = upload.array(fieldName, maxCount);
    
    uploadMiddleware(req, res, async (err) => {
      if (err) {
        if (err instanceof multer.MulterError) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
              success: false,
              error: 'File too large. Maximum size is 10MB.'
            });
          }
          if (err.code === 'LIMIT_UNEXPECTED_FILE') {
            return res.status(400).json({
              success: false,
              error: `Too many files. Maximum is ${maxCount}.`
            });
          }
        }
        return res.status(400).json({
          success: false,
          error: err.message
        });
      }
      
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'No image files provided.'
        });
      }
      
      try {
        const processedImages = [];
        
        for (let i = 0; i < req.files.length; i++) {
          const file = req.files[i];
          
          // Generate unique filename
          const timestamp = Date.now();
          const randomString = Math.random().toString(36).substring(2, 15);
          const filename = `${timestamp}_${randomString}_${i}`;
          
          // Process the image
          const result = await processImage(file.buffer, filename);
          processedImages.push(result);
        }
        
        // Add processed images info to request
        req.processedImages = processedImages;
        
        next();
      } catch (error) {
        return res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });
  };
};

// Delete image file
const deleteImage = async (filename) => {
  try {
    const uploadsDir = await createUploadsDir();
    const filepath = path.join(uploadsDir, filename);
    await fs.unlink(filepath);
    return true;
  } catch (error) {
    console.error('Error deleting image:', error);
    return false;
  }
};

module.exports = {
  uploadSingle,
  uploadMultiple,
  processImage,
  deleteImage,
  createUploadsDir
};
